from playwright.sync_api import sync_playwright
import time

def demo_presentation():
    """自动化项目介绍脚本 - 登录系统"""
    with sync_playwright() as p:
        # 启动浏览器，设置真正的全屏模式
        browser = p.chromium.launch(
            headless=False,
            args=[
                '--start-fullscreen',  # 真正的全屏模式
                '--kiosk',  # 类似信息亭模式，隐藏浏览器UI
                '--disable-infobars',  # 禁用信息栏
                '--disable-extensions',  # 禁用扩展
                '--no-first-run',  # 跳过首次运行提示
                '--disable-features=VizDisplayCompositor',  # 禁用某些特性以获得更好的性能
            ]
        )
        
        # 创建上下文，设置全屏视口
        context = browser.new_context(
            viewport={'width': 1920, 'height': 950},  # 全高清分辨率
            no_viewport=False  # 确保使用设置的视口
        )
        page = context.new_page()

        try:
            # 打开登录页面
            print("正在打开登录页面...")
            page.goto("http://218.25.141.19:8075/webroot/decision/view/duchamp?viewlet=00%25E5%25B9%25B3%25E5%258F%25B0%25E7%25B4%25A2%25E5%25BC%2595%252F%25E6%2589%25A7%25E6%25B3%2595%25E8%25B4%25A8%25E6%2595%2588%25E8%25AF%2584%25E4%25BC%25B0%25E5%25B9%25B3%25E5%258F%25B0.fvs&ref_t=design&ref_c=8cf44e88-ff01-463d-9e77-443b236bb210&page_number=1", 
                     wait_until='networkidle')

            # 等待页面加载完成
            page.wait_for_timeout(2000)  # 额外等待2秒确保完全加载

            # 登录凭据
            username = "18741759096"
            password = "Nan1230."

            # 输入用户名 - 使用更精确的选择器
            print("正在输入用户名...")
            username_selectors = [
                'input[type="text"]',
                'input[name="username"]',
                'input#username',
                '[id*="user"] input',
                'input:not([type="password"])'  # 第一个非密码输入框
            ]
            
            for selector in username_selectors:
                if page.locator(selector).count() > 0:
                    page.fill(selector, username)
                    print(f"用户名输入完成")
                    break

            # 输入密码 - 使用更精确的选择器
            print("正在输入密码...")
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                'input#password',
                '[id*="pass"] input'
            ]
            
            for selector in password_selectors:
                if page.locator(selector).count() > 0:
                    page.fill(selector, password)
                    print(f"密码输入完成")
                    break

            # 直接使用回车键登录
            print("使用回车键登录...")
            page.keyboard.press('Enter')

            # 等待登录完成
            print("等待登录完成...")
            page.wait_for_timeout(5000)  # 等待5秒确保登录完成

            # 检查登录是否成功
            current_url = page.url
            if "login" not in current_url.lower():
                print("✓ 登录成功！")
                
                
                # 截图保存最终状态
                page.screenshot(path="final_demo_page.png", full_page=True)
                
                print("=" * 60)
                print("🎯 项目介绍演示准备完成！")
                print(f"📊 当前页面: {page.title()}")
                print("🖥️  浏览器已设置为全屏模式")
                print("📏 分辨率: 1920x1080 (全高清)")
                print("🚫 已禁用滚动条和右键菜单")
                print("🚀 可以开始项目介绍演示")
                print("=" * 60)

            else:
                print("❌ 登录失败，请检查凭据或网络连接")
                page.screenshot(path="login_failed.png")

            # 保持浏览器打开用于演示
            input("按回车键结束演示...")

        except Exception as e:
            print(f"❌ 发生错误: {e}")
            page.screenshot(path="error.png")
            import traceback
            traceback.print_exc()

        finally:
            # 演示结束后关闭浏览器
            browser.close()
            print("演示结束")

if __name__ == "__main__":
    demo_presentation()
