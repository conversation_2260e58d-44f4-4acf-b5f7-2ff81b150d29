from playwright.sync_api import sync_playwright
import time

def demo_presentation():
    """自动化项目介绍脚本 - 登录系统并展示前方安保指挥中心"""
    with sync_playwright() as p:
        # 启动浏览器，全屏模式
        browser = p.chromium.launch(
            headless=False,
            args=['--start-fullscreen', '--disable-web-security']
        )
        context = browser.new_context(
            viewport={'width': 1920, 'height': 1080}  # 设置视口大小
        )
        page = context.new_page()

        try:
            # 打开登录页面
            print("正在打开登录页面...")
            page.goto("http://218.25.141.19:8075/webroot/decision/login?origin=5fd55ee2-0157-4777-a8dc-cff129101bbf")

            # 等待页面加载完成
            page.wait_for_load_state('networkidle')

            # 登录凭据
            username = "18741759096"
            password = "Nan1230."

            # 截图查看登录页面结构
            page.screenshot(path="login_page_debug.png")
            print("已保存登录页面截图用于调试")

            # 输入用户名
            print("正在输入用户名...")
            username_selectors = [
                'input[type="text"]',
                'input[name="username"]',
                'input[name="user"]',
                'input[placeholder*="用户"]',
                'input[placeholder*="账号"]',
                '#username'
            ]

            username_filled = False
            for selector in username_selectors:
                elements = page.locator(selector)
                if elements.count() > 0:
                    page.fill(selector, username)
                    username_filled = True
                    print(f"用户名输入成功，使用选择器: {selector}")
                    break

            if not username_filled:
                print("未找到用户名输入框！")
                # 打印所有input元素来调试
                inputs = page.locator('input').all()
                print("页面上的所有input元素:")
                for i, inp in enumerate(inputs):
                    try:
                        inp_type = inp.get_attribute('type') or 'text'
                        inp_name = inp.get_attribute('name') or ''
                        inp_placeholder = inp.get_attribute('placeholder') or ''
                        print(f"  {i+1}. type={inp_type}, name={inp_name}, placeholder={inp_placeholder}")
                    except:
                        pass
                return

            # 输入密码
            print("正在输入密码...")
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                'input[placeholder*="密码"]',
                '#password'
            ]

            password_filled = False
            for selector in password_selectors:
                elements = page.locator(selector)
                if elements.count() > 0:
                    page.fill(selector, password)
                    password_filled = True
                    print(f"密码输入成功，使用选择器: {selector}")
                    break

            if not password_filled:
                print("未找到密码输入框！")
                return

            # 等待一下让输入生效
            time.sleep(1)

            # 点击登录按钮
            print("正在点击登录按钮...")
            login_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("登录")',
                'button:has-text("登 录")',
                '.login-btn',
                '#loginButton',
                'button'
            ]

            login_clicked = False
            for selector in login_selectors:
                elements = page.locator(selector)
                if elements.count() > 0:
                    print(f"找到登录按钮，使用选择器: {selector}")
                    elements.first.click()
                    login_clicked = True
                    break

            if not login_clicked:
                print("未找到登录按钮，尝试按回车键")
                page.keyboard.press('Enter')

            # 等待登录完成
            print("等待登录完成...")
            page.wait_for_load_state('networkidle')
            time.sleep(3)

            # 检查登录状态
            current_url = page.url
            print(f"登录后URL: {current_url}")

            # 截图查看登录后状态
            page.screenshot(path="after_login.png")
            print("已保存登录后截图")

            # 检查是否还在登录页面
            if "login" in current_url.lower():
                print("警告：似乎还在登录页面，登录可能失败")
                # 检查是否有错误信息
                error_messages = page.locator('text=/错误|失败|无效/')
                if error_messages.count() > 0:
                    print(f"发现错误信息: {error_messages.first.text_content()}")
                return

            print("登录成功！正在查找前方安保指挥中心...")

            # 等待主页面加载
            time.sleep(2)

            # 打印页面标题和可见文本来调试
            print(f"页面标题: {page.title()}")

            # 查找前方安保指挥中心菜单
            security_center_selectors = [
                'text=前方安保指挥中心',
                '*:has-text("前方安保指挥中心")',
                'a:has-text("前方安保指挥中心")',
                'li:has-text("前方安保指挥中心")',
                '[title*="前方安保"]'
            ]

            security_center_found = False
            for selector in security_center_selectors:
                elements = page.locator(selector)
                if elements.count() > 0:
                    print(f"找到前方安保指挥中心菜单，使用选择器: {selector}")
                    elements.first.click()
                    security_center_found = True
                    break

            if not security_center_found:
                print("未找到前方安保指挥中心菜单")
                # 打印所有可见的菜单项来调试
                menu_items = page.locator('li, a, button').all()
                print("可见的菜单项:")
                for i, item in enumerate(menu_items[:10]):  # 只显示前10个
                    try:
                        text = item.text_content()
                        if text and text.strip():
                            print(f"  {i+1}. {text.strip()}")
                    except:
                        pass

            # 等待页面加载
            page.wait_for_load_state('networkidle')
            time.sleep(2)

            # 确保页面全屏显示
            page.keyboard.press('F11')  # 按F11进入全屏模式

            print("项目介绍演示准备完成！")
            print(f"当前页面URL: {page.url}")
            print("浏览器已全屏显示，可以开始演示")

            # 保持浏览器打开用于演示
            input("按回车键结束演示...")

        except Exception as e:
            print(f"发生错误: {e}")
            page.screenshot(path="error.png")

        finally:
            # 演示结束后关闭浏览器
            browser.close()
            print("演示结束")

if __name__ == "__main__":
    demo_presentation()