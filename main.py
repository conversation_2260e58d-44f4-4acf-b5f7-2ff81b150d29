from playwright.sync_api import sync_playwright
import time

def demo_presentation():
    """自动化项目介绍脚本 - 登录执法效能评估平台"""
    with sync_playwright() as p:
        # 启动浏览器，设置真正的全屏模式
        browser = p.chromium.launch(
            headless=False,
            args=[
                '--start-fullscreen',  # 真正的全屏模式
                '--kiosk',  # 类似信息亭模式，隐藏浏览器UI
                '--disable-infobars',  # 禁用信息栏
                '--disable-extensions',  # 禁用扩展
                '--no-first-run',  # 跳过首次运行提示
                '--disable-features=VizDisplayCompositor',  # 禁用某些特性以获得更好的性能
            ]
        )
        
        # 创建上下文，设置全屏视口
        context = browser.new_context(
            viewport={'width': 1920, 'height': 950},  # 全高清分辨率
            no_viewport=False  # 确保使用设置的视口
        )
        page = context.new_page()

        try:
            # 第一步：先打开登录页面进行登录
            print("第一步：打开登录页面...")
            page.goto("http://218.25.141.19:8075/webroot/decision/view/duchamp?viewlet=00%25E5%25B9%25B3%25E5%258F%25B0%25E7%25B4%25A2%25E5%25BC%2595%252F%25E6%2589%25A7%25E6%25B3%2595%25E8%25B4%25A8%25E6%2595%2588%25E8%25AF%2584%25E4%25BC%25B0%25E5%25B9%25B3%25E5%258F%25B0.fvs&ref_t=design&ref_c=8cf44e88-ff01-463d-9e77-443b236bb210&page_number=1")

            # 等待登录页面加载完成
            page.wait_for_load_state('networkidle')
            time.sleep(2)

            # 登录凭据
            username = "18741759096"
            password = "Nan1230."

            # 输入用户名
            print("正在输入用户名...")
            page.fill('input[type="text"]', username)

            # 输入密码
            print("正在输入密码...")
            page.fill('input[type="password"]', password)

            # 使用回车键登录
            print("使用回车键登录...")
            page.keyboard.press('Enter')

            # 等待登录完成
            print("等待登录完成...")
            page.wait_for_load_state('networkidle')
            time.sleep(3)


            print("✓ 成功进入执法质效评估平台！")

            # 第三步：查找服务机构检查进度模块的全屏工具
            print("第三步：查找服务机构检查进度模块...")

            # 查找服务机构检查进度相关文本
            progress_elements = page.locator('*:has-text("服务机构检查进度")')
            if progress_elements.count() > 0:
                print("✓ 找到服务机构检查进度模块")

                # 在该模块附近查找全屏按钮
                print("查找全屏显示工具...")
                fullscreen_selectors = [
                    'i.fa-expand',
                    'i.fa-fullscreen',
                    'i.fa-arrows-alt',
                    'button[title*="全屏"]',
                    'button[title*="放大"]',
                    '[class*="fullscreen"]',
                    '[class*="expand"]',
                    '[onclick*="fullscreen"]'
                ]

                fullscreen_found = False
                for selector in fullscreen_selectors:
                    elements = page.locator(selector)
                    if elements.count() > 0:
                        print(f"✓ 找到全屏工具: {selector}")
                        try:
                            elements.first.click()
                            fullscreen_found = True
                            print("✓ 成功点击全屏显示工具")
                            time.sleep(2)
                            break
                        except Exception as e:
                            print(f"点击失败: {e}")
                            continue

                if not fullscreen_found:
                    print("未找到全屏工具，将显示所有可能的按钮供调试")
            else:
                print("未找到服务机构检查进度模块")

            # 截图保存最终状态
            page.screenshot(path="final_demo_page.png", full_page=True)

            print("=" * 60)
            print("🎯 项目介绍演示准备完成！")
            print(f"📊 当前页面: {page.title()}")
            print("🖥️  浏览器已设置为全屏模式")
            print("🚀 可以开始项目介绍演示")
            print("=" * 60)

            # 保持浏览器打开用于演示
            input("按回车键结束演示...")

        except Exception as e:
            print(f"❌ 发生错误: {e}")
            page.screenshot(path="error.png")
            import traceback
            traceback.print_exc()

        finally:
            # 演示结束后关闭浏览器
            browser.close()
            print("演示结束")

if __name__ == "__main__":
    demo_presentation()
