from playwright.sync_api import sync_playwright
import time

def login_to_system():
    with sync_playwright() as p:
        # 启动浏览器（设置 headless=False 可以看到浏览器界面）
        browser = p.chromium.launch(headless=False, slow_mo=1000)  # slow_mo 让操作变慢便于观察
        context = browser.new_context()
        page = context.new_page()
        
        try:
            # 打开登录页面
            print("正在打开登录页面...")
            page.goto("http://218.25.141.19:8075/webroot/decision/login?origin=5fd55ee2-0157-4777-a8dc-cff129101bbf")
            
            # 等待页面加载完成
            page.wait_for_load_state('networkidle')
            
            # 截屏查看页面结构（用于调试）
            page.screenshot(path="login_page.png")
            print("已保存页面截图到 login_page.png")
            
            # 等待用户名输入框出现并输入用户名
            print("正在输入用户名...")
            
            # 方法1：通过选择器输入（需要根据实际页面调整选择器）
            # 常见的用户名输入框选择器可能包括：
            # page.fill('input[type="text"]', 'your_username')
            # page.fill('input[name="username"]', 'your_username')
            # page.fill('#username', 'your_username')
            
            # 方法2：通过定位文本相关的输入框（更通用）
            # 这里需要您查看页面并确定正确的选择器
            
            # 示例：假设页面结构，您需要根据实际情况调整
            # 请替换 'your_username' 和 'your_password' 为实际的登录凭据
            username = "18741759096"  # 替换为您的用户名
            password = "Nan1230."  # 替换为您的密码
            
            # 尝试多种可能的选择器
            selectors_to_try = [
                'input[type="text"]',
                'input[name="username"]',
                'input[name="user"]',
                '#username',
                '.username-input',
                'input[placeholder*="用户"]',
                'input[placeholder*="账号"]'
            ]
            
            username_filled = False
            for selector in selectors_to_try:
                if page.locator(selector).count() > 0:
                    page.fill(selector, username)
                    username_filled = True
                    print(f"使用选择器 {selector} 输入用户名")
                    break
            
            if not username_filled:
                print("未找到用户名输入框，请手动检查页面结构")
                # 您可以在这里暂停以便手动检查
                input("请按回车键继续...")
            
            # 输入密码
            print("正在输入密码...")
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                '#password',
                '.password-input',
                'input[placeholder*="密码"]'
            ]
            
            password_filled = False
            for selector in password_selectors:
                if page.locator(selector).count() > 0:
                    page.fill(selector, password)
                    password_filled = True
                    print(f"使用选择器 {selector} 输入密码")
                    break
            
            if not password_filled:
                print("未找到密码输入框，请手动检查页面结构")
                input("请按回车键继续...")
            
            # 点击登录按钮
            print("正在点击登录按钮...")
            login_button_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("登录")',
                '.login-btn',
                '#loginButton'
            ]
            
            login_clicked = False
            for selector in login_button_selectors:
                if page.locator(selector).count() > 0:
                    page.click(selector)
                    login_clicked = True
                    print(f"使用选择器 {selector} 点击登录")
                    break
            
            if not login_clicked:
                # 如果以上选择器都不行，尝试按回车键
                page.keyboard.press('Enter')
                print("按回车键尝试登录")
            
            # 等待登录完成
            print("等待登录结果...")
            time.sleep(3)
            
            # 检查是否登录成功
            current_url = page.url
            print(f"当前URL: {current_url}")
            
            # 截屏查看登录后的页面
            page.screenshot(path="after_login.png")
            print("已保存登录后截图到 after_login.png")
            
            # 检查是否有登录成功的迹象
            if "login" not in current_url.lower():
                print("登录可能成功！")
                # 可以在这里添加登录成功后的操作
                time.sleep(5)  # 停留5秒观察结果
            else:
                print("登录可能失败，请检查凭据或页面结构")
                
        except Exception as e:
            print(f"发生错误: {e}")
            page.screenshot(path="error.png")
            print("已保存错误截图到 error.png")
            
        finally:
            # 保持浏览器打开以便调试，注释掉下一行来自动关闭
            # browser.close()
            print("脚本执行完成")

if __name__ == "__main__":
    login_to_system()