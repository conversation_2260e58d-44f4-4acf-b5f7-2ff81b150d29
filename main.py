from playwright.sync_api import sync_playwright
import time

def demo_presentation():
    """自动化项目介绍脚本 - 登录系统并展示前方安保指挥中心"""
    with sync_playwright() as p:
        # 启动浏览器，全屏模式
        browser = p.chromium.launch(
            headless=False,
            args=['--start-fullscreen', '--disable-web-security']
        )
        context = browser.new_context(
            viewport={'width': 1920, 'height': 1080}  # 设置视口大小
        )
        page = context.new_page()

        try:
            # 打开登录页面
            print("正在打开登录页面...")
            page.goto("http://218.25.141.19:8075/webroot/decision/login?origin=5fd55ee2-0157-4777-a8dc-cff129101bbf")

            # 等待页面加载完成
            page.wait_for_load_state('networkidle')

            # 登录凭据
            username = "18741759096"
            password = "Nan1230."

            # 输入用户名
            print("正在输入用户名...")
            username_selectors = [
                'input[type="text"]',
                'input[name="username"]',
                'input[placeholder*="用户"]'
            ]

            for selector in username_selectors:
                if page.locator(selector).count() > 0:
                    page.fill(selector, username)
                    print(f"用户名输入成功")
                    break

            # 输入密码
            print("正在输入密码...")
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                'input[placeholder*="密码"]'
            ]

            for selector in password_selectors:
                if page.locator(selector).count() > 0:
                    page.fill(selector, password)
                    print(f"密码输入成功")
                    break

            # 点击登录按钮
            print("正在登录...")
            login_selectors = [
                'button[type="submit"]',
                'button:has-text("登录")',
                'input[type="submit"]'
            ]

            for selector in login_selectors:
                if page.locator(selector).count() > 0:
                    page.click(selector)
                    break

            # 等待登录完成
            print("等待登录完成...")
            page.wait_for_load_state('networkidle')
            time.sleep(2)

            # 进入前方安保指挥中心
            print("正在进入前方安保指挥中心...")

            # 查找并点击前方安保指挥中心菜单项
            security_center_selectors = [
                'text=前方安保指挥中心',
                '[title="前方安保指挥中心"]',
                'a:has-text("前方安保指挥中心")',
                'li:has-text("前方安保指挥中心")'
            ]

            security_center_found = False
            for selector in security_center_selectors:
                if page.locator(selector).count() > 0:
                    page.click(selector)
                    security_center_found = True
                    print("成功点击前方安保指挥中心")
                    break

            if not security_center_found:
                print("未找到前方安保指挥中心菜单，尝试其他方式...")
                # 可以尝试通过URL直接访问
                # page.goto("具体的前方安保指挥中心URL")

            # 等待页面加载
            page.wait_for_load_state('networkidle')
            time.sleep(2)

            # 确保页面全屏显示
            page.keyboard.press('F11')  # 按F11进入全屏模式

            print("项目介绍演示准备完成！")
            print("当前页面：前方安保指挥中心")
            print("浏览器已全屏显示，可以开始演示")

            # 保持浏览器打开用于演示
            input("按回车键结束演示...")

        except Exception as e:
            print(f"发生错误: {e}")
            page.screenshot(path="error.png")

        finally:
            # 演示结束后关闭浏览器
            browser.close()
            print("演示结束")

if __name__ == "__main__":
    demo_presentation()