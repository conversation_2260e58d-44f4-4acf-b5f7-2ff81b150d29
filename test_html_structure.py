from playwright.sync_api import sync_playwright
import time

def test_html_structure():
    """临时测试脚本 - 查看前端HTML结构并操作全屏显示"""
    with sync_playwright() as p:
        # 启动浏览器，全屏模式
        browser = p.chromium.launch(
            headless=False,
            args=['--start-fullscreen', '--disable-web-security']
        )
        context = browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        page = context.new_page()
        
        try:
            # 第一步：先打开登录页面
            print("第一步：打开登录页面...")
            page.goto("http://218.25.141.19:8075/webroot/decision/view/duchamp?viewlet=00%25E5%25B9%25B3%25E5%258F%25B0%25E7%25B4%25A2%25E5%25BC%2595%252F%25E6%2589%25A7%25E6%25B3%2595%25E8%25B4%25A8%25E6%2595%2588%25E8%25AF%2584%25E4%25BC%25B0%25E5%25B9%25B3%25E5%258F%25B0.fvs&ref_t=design&ref_c=8cf44e88-ff01-463d-9e77-443b236bb210&page_number=1")

            # 等待登录页面加载完成
            page.wait_for_load_state('networkidle')
            time.sleep(2)

            # 登录凭据
            username = "18741759096"
            password = "Nan1230."

            # 输入用户名
            print("正在输入用户名...")
            page.fill('input[type="text"]', username)

            # 输入密码
            print("正在输入密码...")
            page.fill('input[type="password"]', password)

            # 使用回车键登录
            print("使用回车键登录...")
            page.keyboard.press('Enter')

            # 等待登录完成
            print("等待登录完成...")
            page.wait_for_load_state('networkidle')
            time.sleep(3)

            print("✓ 成功进入执法质效评估平台页面")
            
            print("页面加载完成，正在查找服务机构检查进度模块...")

            # 截图查看当前页面结构
            page.screenshot(path="current_page.png", full_page=True)
            print("已保存当前页面截图")

            # 查找服务机构检查进度模块
            print("查找服务机构检查进度模块...")
            progress_selectors = [
                'text=服务机构检查进度',
                '*:has-text("服务机构检查进度")',
                'div:has-text("检查进度")',
                'span:has-text("检查进度")',
                '*:has-text("服务机构") >> .. >> *:has-text("进度")'
            ]

            progress_module_found = False
            for selector in progress_selectors:
                elements = page.locator(selector)
                if elements.count() > 0:
                    print(f"✓ 找到服务机构检查进度模块")
                    progress_module_found = True

                    # 在该模块附近查找全屏按钮
                    print("在模块附近查找全屏显示工具...")

                    # 查找全屏按钮的多种可能选择器
                    fullscreen_selectors = [
                        # 通用全屏图标
                        'i.fa-expand',
                        'i.fa-fullscreen',
                        'i.fa-arrows-alt',
                        # 按钮相关
                        'button[title*="全屏"]',
                        'button[title*="放大"]',
                        'button[title*="最大化"]',
                        # 类名相关
                        '.fullscreen-btn',
                        '.expand-btn',
                        '.maximize-btn',
                        # 通过图标查找
                        '[class*="expand"]',
                        '[class*="fullscreen"]',
                        '[class*="maximize"]'
                    ]

                    fullscreen_found = False
                    for fs_selector in fullscreen_selectors:
                        fs_elements = page.locator(fs_selector)
                        if fs_elements.count() > 0:
                            print(f"✓ 找到全屏按钮: {fs_selector}")
                            try:
                                fs_elements.first.click()
                                fullscreen_found = True
                                print("✓ 成功点击全屏显示工具")
                                time.sleep(2)  # 等待全屏效果
                                break
                            except Exception as e:
                                print(f"点击失败: {e}")
                                continue

                    if not fullscreen_found:
                        print("未找到全屏按钮，打印所有可能的图标元素...")
                        # 查找所有可能的图标和按钮
                        icons = page.locator('i, button, [class*="icon"], [class*="btn"]').all()
                        print("页面上的图标和按钮元素:")
                        for i, icon in enumerate(icons[:20]):  # 显示前20个
                            try:
                                title = icon.get_attribute('title') or ''
                                class_name = icon.get_attribute('class') or ''
                                onclick = icon.get_attribute('onclick') or ''
                                print(f"  {i+1}. class='{class_name}', title='{title}', onclick='{onclick[:50]}'")
                            except:
                                pass
                    break

            if not progress_module_found:
                print("未找到服务机构检查进度模块")
                # 打印页面上所有文本内容来调试
                all_text = page.locator('*').all()
                print("页面上包含'服务'或'机构'的文本:")
                for element in all_text[:30]:
                    try:
                        text = element.text_content()
                        if text and ('服务' in text or '机构' in text or '进度' in text):
                            print(f"  - {text.strip()}")
                    except:
                        pass
            
            # 等待操作完成
            time.sleep(2)
            
            # 截图保存当前状态
            page.screenshot(path="test_fullscreen.png")
            print("已保存测试截图")
            
            print("=" * 50)
            print("测试完成！")
            print("请查看浏览器中的效果")
            print("=" * 50)
            
            # 保持浏览器打开
            input("按回车键结束测试...")
                
        except Exception as e:
            print(f"发生错误: {e}")
            page.screenshot(path="test_error.png")
            
        finally:
            browser.close()
            print("测试结束")

if __name__ == "__main__":
    test_html_structure()
