from playwright.sync_api import sync_playwright
import time

def test_html_structure():
    """临时测试脚本 - 查看前端HTML结构并操作全屏显示"""
    with sync_playwright() as p:
        # 启动浏览器，全屏模式
        browser = p.chromium.launch(
            headless=False,
            args=['--start-fullscreen', '--disable-web-security']
        )
        context = browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        page = context.new_page()
        
        try:
            # 打开登录页面
            print("正在打开登录页面...")
            page.goto("http://218.25.141.19:8075/webroot/decision/login?origin=5fd55ee2-0157-4777-a8dc-cff129101bbf")
            
            # 等待页面加载完成
            page.wait_for_load_state('networkidle')
            
            # 登录凭据
            username = "18741759096"
            password = "Nan1230."
            
            # 输入用户名和密码
            print("正在登录...")
            page.fill('input[type="text"]', username)
            page.fill('input[type="password"]', password)
            page.click('button:has-text("登录")')
            
            # 等待登录完成
            page.wait_for_load_state('networkidle')
            time.sleep(3)
            
            print("登录完成，正在查找中介服务机构数量分布...")
            
            # 查找中介服务机构数量分布相关元素
            service_agency_selectors = [
                'text=中介服务机构数量分布',
                '*:has-text("中介服务机构数量分布")',
                'text=服务机构检查进度',
                '*:has-text("服务机构检查进度")',
                'div:has-text("中介服务机构")',
                'span:has-text("中介服务机构")'
            ]
            
            print("查找中介服务机构相关模块...")
            for selector in service_agency_selectors:
                elements = page.locator(selector)
                if elements.count() > 0:
                    print(f"找到模块: {selector}")
                    # 不立即点击，先查看结构
                    
            # 查找服务机构检查进度模块
            print("查找服务机构检查进度模块...")
            progress_selectors = [
                'text=服务机构检查进度',
                '*:has-text("服务机构检查进度")',
                'div:has-text("检查进度")',
                'span:has-text("检查进度")'
            ]
            
            progress_module = None
            for selector in progress_selectors:
                elements = page.locator(selector)
                if elements.count() > 0:
                    print(f"找到服务机构检查进度模块: {selector}")
                    progress_module = elements.first
                    break
            
            if progress_module:
                # 查找该模块内的全屏显示工具
                print("查找全屏显示工具...")
                
                # 在服务机构检查进度模块附近查找全屏按钮
                fullscreen_selectors = [
                    'button[title*="全屏"]',
                    'button[title*="放大"]',
                    'i.fa-expand',
                    'i.fa-fullscreen',
                    '.fullscreen-btn',
                    'button:has-text("全屏")',
                    '[data-action*="fullscreen"]',
                    '[onclick*="fullscreen"]'
                ]
                
                fullscreen_found = False
                for selector in fullscreen_selectors:
                    elements = page.locator(selector)
                    if elements.count() > 0:
                        print(f"找到全屏按钮: {selector}")
                        # 检查是否在服务机构检查进度模块附近
                        for element in elements.all():
                            try:
                                # 点击全屏按钮
                                element.click()
                                fullscreen_found = True
                                print("成功点击全屏显示工具")
                                break
                            except Exception as e:
                                print(f"点击全屏按钮失败: {e}")
                                continue
                        if fullscreen_found:
                            break
                
                if not fullscreen_found:
                    print("未找到全屏显示工具")
                    # 打印页面上所有可能的按钮
                    buttons = page.locator('button, i[class*="fa-"], [title*="全屏"], [title*="放大"]').all()
                    print("页面上可能的全屏相关元素:")
                    for i, btn in enumerate(buttons[:15]):  # 显示前15个
                        try:
                            title = btn.get_attribute('title') or ''
                            class_name = btn.get_attribute('class') or ''
                            text = btn.text_content() or ''
                            print(f"  {i+1}. title='{title}', class='{class_name}', text='{text.strip()}'")
                        except:
                            pass
            
            # 等待操作完成
            time.sleep(2)
            
            # 截图保存当前状态
            page.screenshot(path="test_fullscreen.png")
            print("已保存测试截图")
            
            print("=" * 50)
            print("测试完成！")
            print("请查看浏览器中的效果")
            print("=" * 50)
            
            # 保持浏览器打开
            input("按回车键结束测试...")
                
        except Exception as e:
            print(f"发生错误: {e}")
            page.screenshot(path="test_error.png")
            
        finally:
            browser.close()
            print("测试结束")

if __name__ == "__main__":
    test_html_structure()
